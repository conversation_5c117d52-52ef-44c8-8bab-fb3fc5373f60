<?php

  trait quotationStep2Actions {


    public function executeWizardstep2() {
      if (!isset($_SESSION["wizard"]['quotation'])) {
        ResponseHelper::redirect($this->wizardurl);
      }

      $this->step = 2;
      $quotation = $_SESSION["wizard"]['quotation'];
      $this->quotation = $quotation;

      if ($quotation->stoneId == "") {
        ResponseHelper::redirectAlertMessage("Deze offerte heeft geen steen in stap 2. Dat is niet mogelijk.", $this->wizardurl);
      }

      $this->stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      if (!$this->stone) {
        ResponseHelper::redirectAlertMessage("Deze offerte heeft geen steen in stap 2. Dat is niet mogelijk.", $this->wizardurl);
      }

      if ($this->stone->isNatuursteen() && $this->stone->isVensterbank()) {
        if (isset($_GET["json"])) {
          if (isset($_POST["next"]) || isset($_POST["prev"])) {
            $this->step2VensterbankPost();
          }
          else {
            $this->step2VensterbankGet();
          }
        }
        $this->stepname = "step2Windowsill";
      }
      elseif ($this->stone->isNatuursteen() && $this->stone->isBalkje()) {
        if (isset($_GET["json"])) {
          if (isset($_POST["next"]) || isset($_POST["prev"])) {
            $this->step2BalkjePost();
          }
          else {
            $this->step2BalkjeGet();
          }
        }
        $this->stepname = "step2Beam";
      }
      else {
        if (isset($_GET["json"])) {
          if (isset($_POST["next"]) || isset($_POST["prev"])) {
            $this->step2StandardPost();
          }
          else {
            $this->step2StandardGet();
          }
        }
        $this->stepname = "step2";
      }
    }

    public function step2VensterbankGet() {

      if (!isset($_SESSION["wizard"]['quotation'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      $quotation = $_SESSION["wizard"]['quotation'];

      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      else {
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }
      $stone_size = StoneSizes::find_by(["sizeId" => $quotation->sizeId]);
      $empty_windowsill = new Windowsill();
      $empty_windowsill->id = -1;
      $empty_windowsill->name = "Selecteer bovenaanzicht...";
      $empty_windowsill->sort = -1;
      $windowsillsTemplates[-1] = $empty_windowsill;
      $windowsillsTemplates += AppModel::mapObjectIds(Windowsill::find_all("ORDER BY sort"));

      $elements = [];

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';
      $element_new->isShown = false;
      $element_new->amount = 1;

      $new_windowsill = new OrderElementWindowsill();
      foreach ($windowsillsTemplates as $windowsill) {
        $new_windowsill->windowsill_id = $windowsill->id;
        break;
      }
      $element_new->windowsill = $new_windowsill;

      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];
        foreach ($elements as $k => $element) {
          $elements[$k]->oldkey = $k;
          $elements[$k]->isShown = false;
        }
      }
      else {
        //voor de eerste keer op deze pagina
        $el = ObjectHelper::copyObject($element_new);
        $el->referenceName = RdeHelper::intToAlpha(0);
        $el->amount = 1;
        $el->windowsill = $new_windowsill;
        $el->isShown = true;
        $elements[] = $el;
      }

      $data = new stdClass();
      $data->step = 2;
      $data->quotation = $quotation;
      $data->quotation_extra = $quotation_extra;
      $data->stone = $this->stone;
      $data->stone_size = $stone_size;
      $data->elements = $elements;
      $data->element_new = $element_new;
      $data->windowsillsTemplates = $windowsillsTemplates;
      $data->errors = [];
      ResponseHelper::responseSuccess($data);

    }

    public function step2VensterbankPost() {

      if (!isset($_SESSION["wizard"]['quotation'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      $response = [];
      $response["errors"] = [];

      $quotation = $_SESSION["wizard"]['quotation'];

      $quotation->shorter = 0;

      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      else {
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }


      $windowsillsTemplates = AppModel::mapObjectIds(Windowsill::find_all("ORDER BY sort"));

      $elements = [];

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';
      $element_new->isShown = false;
      $element_new->amount = 1;

      $new_windowsill = new OrderElementWindowsill();
      foreach ($windowsillsTemplates as $windowsill) {
        $new_windowsill->windowsill_id = $windowsill->id;
        break;
      }
      $element_new->windowsill = $new_windowsill;

      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];
        foreach ($elements as $k => $element) {
          $elements[$k]->oldkey = $k;
          $elements[$k]->isShown = false;
        }
      }
      else {
        //voor de eerste keer op deze pagina
        $el = ObjectHelper::copyObject($element_new);
        $el->referenceName = RdeHelper::intToAlpha(0);
        $el->amount = 1;
        $el->windowsill = $new_windowsill;
        $el->isShown = true;
        $elements[] = $el;
      }

      //mappen key aan element
      $elements_oldkey = [];
      foreach ($elements as $k => $element) {
        $elements_oldkey[$k] = $element;
      }

      $elements_new = [];
      foreach ($_POST['elements'] as $k => $elementin) {
        /** @var OrderElements $element */
        if (isset($elementin["oldkey"]) && $elementin["oldkey"] != "" && isset($elements_oldkey[$elementin["oldkey"]])) {
          //in de post zit de orginelekey (oldkey), en er deze bestaat ook in de elementen, dan mappen.
          $element = $elements_oldkey[$elementin["oldkey"]];
        }
        else {
          //niet gevonden, nieuw element
          $element = ObjectHelper::copyObject($element_new);
        }

        if (!isset($elementin['amount'])) continue; //nog niks geselecteerd. verwijderen element

        $element->referenceName = trim($elementin['referenceName']);
        $element->amount = intval(trim($elementin['amount']));
        $element->flagWindow = 'false';
        $element->elementLength = 0;//intval(trim($elementin['elementLength']));

        $element->leftEndstone = 0;
        $element->rightEndstone = 0;
        $element->leftEndstoneGrooves = 0;
        $element->rightEndstoneGrooves = 0;

        if (!isset($element->windowsill)) {
          $element->windowsill = new OrderElementWindowsill();
        }
        $element->windowsill->windowsill_id = $elementin['windowsill_id'];
        $element->windowsill->x1 = isset($elementin['x1']) ? $elementin['x1'] : null;
        $element->windowsill->x2 = isset($elementin['x2']) ? $elementin['x2'] : null;
        $element->windowsill->x3 = isset($elementin['x3']) ? $elementin['x3'] : null;
        $element->windowsill->x4 = isset($elementin['x4']) ? $elementin['x4'] : null;
        $element->windowsill->x5 = isset($elementin['x5']) ? $elementin['x5'] : null;
        $element->windowsill->x6 = isset($elementin['x6']) ? $elementin['x6'] : null;
        $element->windowsill->remark_cust = $elementin['remark_cust'];

        $element->inputLength = intval($elementin['x1'] ?? 0);


        $elements_new[$k] = $element;
      }
      $elements = $elements_new;

      //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
      if (isset($_POST["prev"])) {
        $_SESSION["wizard"]['elements'] = $elements;
        $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=1");
      }

      //extra checks backend
      foreach ($elements as $key1 => $element) {
        if ($element->amount == 0 && $element->inputLength == 0) {
          if (count($elements) > 1) unset($elements[$key1]);
          continue;
        }
        if ($element->amount <= 0) {
          $response["errors"]['amount'] = "U dient minimaal 1 stuk van een element te bestellen.";
        }
        foreach ($elements as $key2 => $el) {
          if ($element->windowsill->windowsill_id == -1) {
            $response["errors"]["bovenaanzicht" . $element->referenceName] = "Kenmerk " . $element->referenceName . ": selecteer een bovenaanzicht.";
          }
          elseif ($key1 != $key2 && $el->referenceName == $element->referenceName) {
            $response["errors"][escapeIdForJQ('elements[' . $key1 . '][referenceName]')] = "Kenmerk dient uniek te zijn.";
          }
          else {
            $template = $windowsillsTemplates[$element->windowsill->windowsill_id];
            for ($tel = 1; $tel <= 6; $tel++) {
              $prop = "x" . $tel;
              if ($template->{$prop} != "" && $element->windowsill->{$prop} == "") {
                $response["errors"]["maten" . $element->referenceName] = "Kenmerk " . $element->referenceName . ": alle maten moeten ingevuld zijn.";
              }
            }
          }
        }

      }

      $elements = array_values($elements);

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
      $_SESSION["wizard"]['elements'] = $elements;
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=4");

    }

    public function step2StandardGet() {

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      else {
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $stone_size = StoneSizes::find_by(["sizeId" => $quotation->sizeId]);
      $elements = [];

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';

      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];

        foreach ($elements as $k => $element) {
          $elements[$k]->oldkey = $k;
        }
      }
      else {
        //voor de eerste keer op deze pagina
        for ($i = 0; $i < 5; $i++) {
          $el = ObjectHelper::copyObject($element_new);
          $el->referenceName = RdeHelper::intToAlpha($i);
          $el->amount = 0;
          $elements[] = $el;
        }
      }

//      $muurdiktes = [];
//      if($stone->isMuurafdekker()) {
//        if($stone_size->wall_min=="" || $stone_size->wall_max=="") {
//          MessageFlashCoordinator::addMessageAlert("Minimale en maximale steendikte niet gedefinieerd. Niet mogelijk om te bestellen. Steen maat id: ".$stone_size->sizeId);
//          ResponseHelper::redirect($this->wizardurl);
//        }
//        for($min = $stone_size->wall_min;$min<=$stone_size->wall_max;$min++) {
//          $muurdiktes[] = $min;
//        }
//
//        if(!in_array($quotation_extra->wall_thickness, $muurdiktes)) {
//          //hij staat er niet in, unset
//          $quotation_extra->wall_thickness = null;
//        }
//
//      }

      $data = new stdClass();
      $data->step = 2;
      $data->quotation = $quotation;
      $data->quotation_extra = $quotation_extra;
      $data->stone = $this->stone;
      $data->stone_size = $stone_size;
      $data->errors = [];
      $data->elements = $elements;
      $data->element_new = $element_new;
      $data->showVlagkozijn = $stone->isRaamdorpel();
      $data->showKieseindsteen = $quotation->maySelectEndstone($stone);
      $data->verstekhoektitle = $stone->isSpekband() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS ? 'Hoek' : 'Verstekhoek';
      ResponseHelper::responseSuccess($data);

    }

    public function step2StandardPost() {

      $response = [];
      $response["errors"] = [];

      $quotation = $this->quotation;

      $quotation_extra = new QuotationsExtra();
      $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $elements = [];
      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];
      }

      if (isset($_POST["shorter"])) {
        $quotation->shorter = trim($_POST["shorter"]);
      }
      else {
        $quotation->shorter = 0;
      }

      //mappen key aan element
      $elements_oldkey = [];
      foreach ($elements as $k => $element) {
        $elements_oldkey[$k] = $element;
      }

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';

      $elements_new = [];
      foreach ($_POST['elements'] as $k => $elementin) {
        /** @var OrderElements $element */
        if (isset($elementin["oldkey"]) && $elementin["oldkey"] != "" && isset($elements_oldkey[$elementin["oldkey"]])) {
          //in de post zit de orginelekey (oldkey), en er deze bestaat ook in de elementen, dan mappen.
          $element = $elements_oldkey[$elementin["oldkey"]];
        }
        else {
          //niet gevonden, nieuw element
          $element = ObjectHelper::copyObject($element_new);
        }
        $element->referenceName = trim($elementin['referenceName']);
        $element->amount = intval(trim($elementin['amount']));
        $element->inputLength = intval(trim($elementin['inputLength']));
        $element->flagWindow = empty($elementin['flagWindow']) ? 'false' : $elementin['flagWindow'];
        $element->elementLength = intval(trim($elementin['elementLength']));
        //manual prop
        $element->mitre = $elementin['mitre'] == "" ? null : $elementin['mitre'];
        if ($quotation->maySelectEndstone($stone)) {
          //er zijn eindstenen geselecteerd (showKieseindsteen = true))
          //dit word o.a. gebruikte voor een speciale eindsteen (met andere prijs) voor keramische spekbanden/muurafdekkers
          $element->leftEndstone = $elementin['leftEndstone'];
          $element->rightEndstone = $elementin['rightEndstone'];
        }
        else {
          //er zijn geen eindstenen geselecteerd. bepaald automatisch.
          if ($element->flagWindow == "double") {
            $element->flagWindowSide = "both";
          }
          elseif ($element->flagWindow == "single" && $element->mitre == "left") {
            $element->flagWindowSide = "right";
          }
          elseif ($element->flagWindow == "single" && $element->mitre == "right") {
            $element->flagWindowSide = "left";
          }
          elseif ($element->flagWindow == "single") {
            //geen mitre gezet, maar wel 1 flagwindow dan op both
            $element->flagWindowSide = "both";
          }
          else {
            $element->flagWindowSide = "none";
          }

          $element->leftEndstone = 0;
          $element->rightEndstone = 0;
          $element->leftEndstoneGrooves = 0;
          $element->rightEndstoneGrooves = 0;

          if ($quotation->endstone == 'true') { //
            $element->leftEndstone = 1;
            $element->rightEndstone = 1;
          }
          elseif ($quotation->endstone == 'true_grooves') {
            $element->leftEndstoneGrooves = 1;
            $element->rightEndstoneGrooves = 1;
          }

        }
        $elements_new[$k] = $element;
      }
      $elements = $elements_new;

      if ($stone->isMuurafdekker()) {
        $quotation_extra->wall_thickness = $_POST['wall_thickness'];
      }
      else {
        $quotation_extra->wall_thickness = null;
      }

      //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
      if (isset($_POST["prev"])) {
        $_SESSION["wizard"]['elements'] = $elements;
        $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=1");
      }

      //extra checks backend
      foreach ($elements as $key1 => $element) {
        if ($element->amount == 0 && $element->inputLength == 0) {
          if (count($elements) > 1) unset($elements[$key1]);
          continue;
        }
        if ($element->amount <= 0) {
          $response["errors"][] = "U dient minimaal 1 stuk van een element te bestellen.";
        }
        if ($element->inputLength < 100) {
          $response["errors"][] = "Elke element dient een kozijnmaat van 100 of meer te hebben.";
        }
        foreach ($elements as $key2 => $el) {
          if ($key1 != $key2 && $el->referenceName == $element->referenceName) {
            $response["errors"][] = "Kenmerk dient uniek te zijn.";
          }
        }
      }
      $elements = array_values($elements);

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      //zet de standaard leftMitreId en rightMitreId op 45
      $mitre45 = false;
      if ($stone->isRaamdorpel() && $stone->isKeramiek()) {
        $mitre45 = Mitres::getMitresByStoneId($quotation->stoneId, 45);
      }
      else {
        $mitre45 = Mitres::getMitresByStoneId(Config::get("MITRE_FALLBACK_STONE_ID"), 45);
      }
      $alwaysSet = $stone->isSpekband() && $stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS;//spekband stjoris vast hoeken
      if ($mitre45 && count($mitre45) > 0) {
        foreach ($elements as $element) {
          if ($element->mitre == "left" || $element->mitre == "both") {
            if ($element->leftMitreId == "" || $alwaysSet) {
              $element->leftMitreId = $mitre45[0]->mitreId;
            }
          }
          else {
            $element->leftMitreId = null;
          }
          if ($element->mitre == "right" || $element->mitre == "both") {
            if ($element->rightMitreId == "" || $alwaysSet) {
              $element->rightMitreId = $mitre45[0]->mitreId;
            }
          }
          else {
            $element->rightMitreId = null;
          }
        }
      }

      $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
      $_SESSION["wizard"]['elements'] = $elements;
      if ($this->hasVerstek($quotation, $elements)) {
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=3");
      }
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=4");


    }


    public function step2BalkjeGet() {

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      else {
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $stone_size = StoneSizes::find_by(["sizeId" => $quotation->sizeId]);
      $elements = [];

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      $element_new->width = 0;
      $element_new->height = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';

      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];
        foreach ($elements as $k => $element) {
          $elements[$k]->oldkey = $k;

          if (isset($element->order_element_sizes)) {
            $elements[$k]->width = $element->order_element_sizes["width"]->value;
            $elements[$k]->height = $element->order_element_sizes["height"]->value;
          }

        }
      }
      else {
        //voor de eerste keer op deze pagina
        for ($i = 0; $i < 5; $i++) {
          $el = ObjectHelper::copyObject($element_new);
          $el->referenceName = RdeHelper::intToAlpha($i);
          $el->amount = 0;
          $elements[] = $el;
        }
      }


      $data = new stdClass();
      $data->step = 2;
      $data->quotation = $quotation;
      $data->quotation_extra = $quotation_extra;
      $data->stone = $this->stone;
      $data->stone_size = $stone_size;
      $data->errors = [];
      $data->elements = $elements;
      $data->element_new = $element_new;
      ResponseHelper::responseSuccess($data);

    }

    public function step2BalkjePost() {

      $response = [];
      $response["errors"] = [];

      $quotation = $this->quotation;

      $quotation_extra = new QuotationsExtra();
      $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $elements = [];
      if (isset($_SESSION["wizard"]['elements'])) {
        $elements = $_SESSION["wizard"]['elements'];
      }

      if (isset($_POST["shorter"])) {
        $quotation->shorter = trim($_POST["shorter"]);
      }
      else {
        $quotation->shorter = 0;
      }

      //mappen key aan element
      $elements_oldkey = [];
      foreach ($elements as $k => $element) {
        $elements_oldkey[$k] = $element;
      }

      $element_new = new OrderElements();
      $element_new->referenceName = RdeHelper::intToAlpha(0);
      $element_new->elementLength = 0;
      $element_new->amount = 0;
      $element_new->inputLength = 0;
      $element_new->width = 0;
      $element_new->height = 0;
      //manual props
      $element_new->mitre = $element_new->getMitre();
      $element_new->elementLengthText = '';

      $elements_new = [];
      foreach ($_POST['elements'] as $k => $elementin) {
        /** @var OrderElements $element */
        if (isset($elementin["oldkey"]) && $elementin["oldkey"] != "" && isset($elements_oldkey[$elementin["oldkey"]])) {
          //in de post zit de orginelekey (oldkey), en er deze bestaat ook in de elementen, dan mappen.
          $element = $elements_oldkey[$elementin["oldkey"]];
        }
        else {
          //niet gevonden, nieuw element
          $element = ObjectHelper::copyObject($element_new);
        }
        $element->referenceName = trim($elementin['referenceName']);
        $element->amount = intval(trim($elementin['amount']));
        $element->inputLength = intval(trim($elementin['inputLength']));
        $element->elementLength = $element->inputLength;
        $element->flagWindow = 'false';
        $element->mitre = $elementin['mitre'] == "" ? null : $elementin['mitre'];

        //map on element and on object
        $element->width = intval(trim($elementin['width']));
        $element->height = intval(trim($elementin['height']));


        if (!isset($element->order_element_sizes)) {
          $element->order_element_sizes["width"] = new OrderElementSize();
          $element->order_element_sizes["width"]->code = "width";
          $element->order_element_sizes["height"] = new OrderElementSize();
          $element->order_element_sizes["height"]->code = "height";
        }
        $element->order_element_sizes["width"]->value = $element->width;
        $element->order_element_sizes["height"]->value = $element->height;


        $elements_new[$k] = $element;
      }
      $elements = $elements_new;

      $quotation_extra->wall_thickness = null;

      //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
      if (isset($_POST["prev"])) {
        $_SESSION["wizard"]['elements'] = $elements;
        $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=1");
      }

      //extra checks backend
      foreach ($elements as $key1 => $element) {
        if ($element->amount == 0 && $element->inputLength == 0) {
          if (count($elements) > 1) unset($elements[$key1]);
          continue;
        }
        if ($element->amount <= 0) {
          $response["errors"][] = "U dient minimaal 1 stuk van een element te bestellen.";
        }
        if ($element->inputLength < 100) {
          $response["errors"][] = "Elke element dient een lengte van 100 of meer te hebben.";
        }
        foreach ($elements as $key2 => $el) {
          if ($key1 != $key2 && $el->referenceName == $element->referenceName) {
            $response["errors"][] = "Kenmerk dient uniek te zijn.";
          }
        }
      }
      $elements = array_values($elements);

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      //zet de standaard leftMitreId en rightMitreId op 45
      $mitre45 = Mitres::getMitresByStoneId(Config::get("MITRE_FALLBACK_STONE_ID"), 45);
      if ($mitre45 && count($mitre45) > 0) {
        foreach ($elements as $element) {
          if ($element->mitre == "left" || $element->mitre == "both") {
            if ($element->leftMitreId == "") {
              $element->leftMitreId = $mitre45[0]->mitreId;
            }
          }
          else {
            $element->leftMitreId = null;
          }
          if ($element->mitre == "right" || $element->mitre == "both") {
            if ($element->rightMitreId == "") {
              $element->rightMitreId = $mitre45[0]->mitreId;
            }
          }
          else {
            $element->rightMitreId = null;
          }
        }
      }

      $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
      $_SESSION["wizard"]['elements'] = $elements;
      if ($this->hasVerstek($quotation, $elements)) {
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=3");
      }
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=4");


    }

  }
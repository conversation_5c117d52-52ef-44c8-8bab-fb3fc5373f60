<?php writeErrors($form->getErrors()); ?>

<div id="split">

  <div id="step5" class="wizard contenttxt">

    <h1>Bestelling splitsen: <?php echo $quotation->getQuotationNumberFull() ?></h1>
    Vul hier het aantallen producten in per levering. U kunt producten verplaatsen tussen leveringen met de pijltjes.<br/>
    <PERSON><PERSON> (meest rechtse) deellevering kunt u in een later stadium nog verder splitsen, tenzij deze reeds in productie is.
    <br/>
    <br/>

    <form method="post">

      <table>
        <tr class="header-row">
          <td>Referentie</td>
          <td>Lengte</td>
          <?php for($tel=0;$tel<$parts;$tel++): ?>
            <td style="text-align: center;"><?php echo $lettersToSplit[$tel] ?></td>
            <td></td>
          <?php endfor; ?>
        </tr>
        <?php foreach($elements as $tel=>$element): ?>
          <tr>
            <td><?php echo $element->referenceName ?></td>
            <td><?php echo $element->elementLength ?></td>
            <?php for($tel=0;$tel<$parts;$tel++):
              $key = "amount_".$tel."_".$element->elementId;
              ?>
              <td><?php $form->getElement($key)->render() ?></td>
              <?php if($tel!=$parts-1): ?>
                <td class="splitcopy" data-id="<?php echo $key; ?>">
                  <a href="#" class="copy_left" title="Verplaats 1 naar links"><span class="fa fa-chevron-left"></span></span></a>
                  <a href="#" class="copy_all_left" title="Verplaats alles naar links"><span class="fa fa-chevron-left"></span><span class="fa fa-chevron-left"></span></a>
                  <a href="#" class="copy_all_right" title="Verplaats alles naar rechts"><span class="fa fa-chevron-right"></span><span class="fa fa-chevron-right"></span></a>
                  <a href="#" class="copy_right" title="Verplaats 1 naar rechts"><span class="fa fa-chevron-right"></span></span></a>
                </td>
              <?php endif; ?>
            <?php endfor; ?>
          </tr>
        <?php endforeach; ?>

      </table>
      <Br/>
      Selecteer de gewenste leverweek en vul uw opmerking in per levering.<br/><br/>
      <table class="split_remarks">
        <tr class="header-row">
          <td>Bestelling</td>
          <td>Leverweek</td>
          <td>Opmerking</td>
        </tr>
        <?php for($tel=0;$tel<$parts;$tel++): ?>
          <tr>
            <td class="bold"><?php echo $lettersToSplit[$tel] ?></td>
            <td>
              <div class="select">
                <?php $form->getElement("week_".$tel)->render() ?>
                <div id="<?php echo "alertweek_".$tel ?>" style="padding-top: 10px;display: none;">
                  <?php echo showAlertButton("De gekozen leverweek is eerder dan onze standaard leverweek. Na onze controle, kunt u in uw account bij offertes de definitieve leverweek inzien.") ?>
                </div>
              </div>
            </td>
            <td>
              <?php $form->getElement("remark_".$tel)->render() ?>
            </td>
          </tr>
        <?php endfor; ?>
      </table>


      <button type="submit" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>
      <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Doorgaan"); ?> <i class="fa fa-chevron-right"></i></button>


    </form>
  </div>

</div>


<script type="text/javascript">
  $(document).ready(function() {

    $(".readonly").attr("readonly", true);

    $(".splitcopy a").click(function(event) {
      event.preventDefault();
      var key = $(this).parent().attr("data-id").split("_");

      var input_name = key[0];
      var tel_left = parseInt(key[1]);
      var elementId = key[2];
      var tel_right = tel_left+1;

      var left = $("#"+input_name+"_"+tel_left+"_"+elementId);
      var left_val = parseInt(left.val());
      var right = $("#"+input_name+"_"+tel_right+"_"+elementId);
      var right_val = parseInt(right.val());

      if($(this).hasClass("copy_left")) {
        if(right_val==0) {
          flashColor(left);
          flashColor(right);
          return;
        }
        left.val(left_val+1);
        right.val(right_val-1);
      }
      else if($(this).hasClass("copy_right")) {
        if(left_val==0) {
          flashColor(left);
          flashColor(right);
          return;
        }
        left.val(left_val-1);
        right.val(right_val+1);
      }
      else if($(this).hasClass("copy_all_left")) {
        if(right_val==0) {
          flashColor(left);
          flashColor(right);
          return;
        }
        left.val(right_val+left_val);
        right.val(0);
      }
      else if($(this).hasClass("copy_all_right")) {
        if(left_val==0) {
          flashColor(left);
          flashColor(right);
          return;
        }
        left.val(0);
        right.val(right_val+left_val);
      }

    });

    $(".weekselect").change(function() {
      var dueDateWeekSet = <?php echo $dueweek ?>;
      if(parseInt($(this).val()) < dueDateWeekSet) {
        $("#alert"+$(this).attr("id")).show();
      }
      else {
        $("#alert"+$(this).attr("id")).hide();
      }
    });

  });

  function flashColor(el) {
    el.removeClass("flash");
    setTimeout(function() {
      el.addClass("flash");
    }, 1);
  }
</script>
<style>

  @-webkit-keyframes demo {
    0% {
      border-color: #ce000c;
    }
    100% {
      border-color: #ccc;
    }
  }

  .flash {
    -webkit-animation-name: demo;
    -webkit-animation-duration: 750ms;
    -webkit-animation-iteration-count: 1;
    -webkit-animation-timing-function: ease-out;
  }
  .split_remarks td {
    vertical-align: middle;
  }
  .split_remarks textarea {
    width: 300px;
    max-width: 100%;
  }
</style>
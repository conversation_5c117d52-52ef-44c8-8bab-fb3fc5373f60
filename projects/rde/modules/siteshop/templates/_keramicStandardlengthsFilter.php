<form method="post" id="searchform">
  <div class="row stone_filter">
    <div>
      <div class="select">
        <select name="sl_depth" id="sl_depth">
          <option value=""><?php echo __('Filter op diepte') ?>...</option>
          <?php foreach($depths as $key=>$val): ?>
            <option value="<?php echo $key ?>" <?php writeIfSelectedVal($_SESSION["sl_depth"],$key) ?>><?php echo $val ?></option>
          <?php endforeach; ?>
        </select>
      </div>
    </div>

    <button type="submit" name="filter" id="filter" class="btn" style="background-color: grey">ZOEKEN</button>

    <a href="/offerte?action=wizard" class="btn btn-primary" style="margin-left: auto;" id="new_offer"><i class="icon-calculator"></i> RAAMDORPELS OP MAAT</a>

  </div>
</form>


<?php if(count($products) == 0): ?>
  <div style="padding-top: 15px;">
  Geen producten gevonden.
  </div>
<?php endif ?>

<style>

  .stone_filter {
    display: flex;
    padding: 0;
  }

  .stone_filter > div {
    padding: 0 0 0 10px;
    width: 20%;
  }

  .stone_filter .btn {
    margin-left: 10px;
  }

  @media (max-width: 767px) {
    .stone_filter {
      display: block;
      padding: 0 10px;
    }

    .stone_filter > div {
      width: 100%;
      padding: 0 0 15px 0;
    }

    .stone_filter .btn {
      width: 100%;
    }
  }

</style>



<script type="text/javascript">

  $(document).ready(function(){
    $("#searchform select").change(function() {
      $("#filter").click();
    });
  });

</script>
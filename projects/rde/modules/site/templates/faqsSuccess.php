
<div class="contenttxt">

  <?php echo $page->content->content1 ?>

  <?php if(count($faq_categories)==0): ?>
    Geen veelgestelde vragen gevonden.
  <?php else:
    $allfaqs = [];
    ?>
    <?php foreach ($faq_categories as $_faq_cat): ?>
      <?php if(count($_faq_cat->faq_items) > 0): ?>
<!--        <h2>--><?php //echo $_faq_cat->content->name ?><!--</h2>-->
        <?php foreach ($_faq_cat->faq_items as $_faq_item):
          $allfaqs[] = $_faq_item;
          ?>
          <div class="faq-item">
            <div class="faq-question"><?php echo $_faq_item->content->question ?></div>
            <div class="faq-answer" style="overflow: hidden; display: none;">
              <?php echo $_faq_item->content->answer; ?>
            </div>
          </div>
        <?php endforeach ?>
      <?php endif; ?>
    <?php endforeach ?>
    <?php Faq::writeStructuredDataScript($allfaqs) ?>
  <?php endif; ?>

</div>

<script>
  $(function(){
    $('.faq-question').click(function(){
      if(!$(this).hasClass('collapsed')){
        $('.faq-answer').slideUp('fast');
        $('.faq-question').removeClass('collapsed');
      }
      $(this).parent('.faq-item').find('.faq-question').toggleClass('collapsed');
      $(this).parent('.faq-item').find('.faq-answer').slideToggle('fast');
    });
  });
</script>

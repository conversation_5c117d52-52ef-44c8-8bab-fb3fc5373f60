<section id="basket">
  <div>
    <h1>Winkelmandje</h1>
    <?php writeErrors($errors); ?>
    <?php if(isset($sellmessage) && $sellmessage != ""): ?>
      <div id="sellmessage"><?php echo $sellmessage ?></div>
    <?php endif; ?>
    <div class="container">
      <?php if(!isset($basket['products']) || count($basket['products']) == 0): ?>
        <Br/>
        Geen producten in winkelmandje.
      <?php else: ?>
        <form method="post" action="<?php echo PageMap::getUrl('M_BASKET') ?>" class="basket-form"  style="padding-left: 10px; padding-right: 10px;">
          <div id="basket_row_header" class="row">
            <div class="col6 col12-xs">Productnaam</div>
            <div class="col2 col12-xs">Aantal</div>
            <div class="col2 col12-xs" style="text-align: right">Stukprijs</div>
            <div class="col2 col12-xs" style="text-align: right">Prijs</div>
          </div>
          <?php foreach ($basket['products'] as $tel => $item):
            $product = $item['product'];
            ?>
            <div class="row">
              <div class="col6 col12-xs ">
                <a href="<?php echo $product->getShopUrl() ?>" id="prodlink_<?php echo $tel ?>"><?php echo $item['name']?></a>
              </div>
              <div class="col2 col12-xs ">
                <?php if($product->discountgroup_id==4): ?>
                  <div class="select">
                    <select name="size[<?php echo $tel ?>]" class="sizeselect">
                      <?php
                        $start = 100;
                        $step = 100;
                        if($product->id==676 || $product->id==682) { //ventiklik
                          $start = 500;
                          $step = 500;
                        }
                      ?>
                      <option value="">Selecteer...</option>
                      <?php for($t=$start;$t<=100000;$t+=$step): ?>
                        <option value="<?php echo $t ?>" <?php if($item['size']==$t) echo 'selected' ?>><?php echo $t ?></option>
                        <?php
                        if($t>=5000) $step=1000;
                        if($t>=50000) $step=10000;
                        ?>
                      <?php endfor; ?>
                    </select>
                  </div>
                <?php else: ?>
                  <input title="Aantal producten" class="productsize form-input" type="text"
                         value="<?php echo $item['size'] ?>" name="size[<?php echo $tel ?>]"
                         tabindex="<?php echo $tel + 1 ?>"
                         style="<?php if(STOCK_ENABLED && (Config::get('BLOCK_WHEN_OUT_OF_STOCK', true) || $product->not_in_backorder) && $product->stock < $item['size']): ?>border: 1px solid red;<?php endif; ?> min-width:50px;" />
                  <a href="#" class="minbasket fa fa-minus-circle" title="- 1"></a>
                  <a href="#" class="plusbasket fa fa-plus-circle" title="+ 1"></a>
                <?php endif; ?>

                <a href="#" class="refreshbasket fa fa-refresh" title="Ververs winkelmandje"></a>
                <a href="javascript: void(0)" id="prdel_<?php echo $tel ?>" class="delproduct fa fa-remove" title="<?php echo __("Verwijder product"); ?>"></a>
              </div>
              <div class="col2 col12-xs basket_pieceprice">
                <?php echo $product->price_on_request ?
                  'Op aanvraag' :
                  StringHelper::asMoney($item["pieceprice"])
                ?>
              </div>
              <div class="col2 col12-xs basket_price">
                <?php if ($product->price_on_request): ?>
                  Op aanvraag
                <?php else: ?>
                  <?php echo Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true)
                      ? StringHelper::asMoney($item["totalprice_inc"])
                      : StringHelper::asMoney($item["totalprice"]) ?>
                <?php endif; ?>
              </div>
            </div>
          <?php endforeach; ?>
          <div class="row">
            <div class="col12 basket_subtotal bold">
              Subtotaal:
              <span id="subtotal">
                <?php if ($hasRequestPrices): ?>
                  Prijzen op aanvraag
                <?php else: ?>
                  <?php echo Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true)
                    ? StringHelper::asMoney($basket['subtotal_inc'])
                    : StringHelper::asMoney($basket['subtotal']) ?>
                <?php endif; ?>
             </span>
            </div>
          </div>

          <div>
            <br/>
            <input class="btn btn-secondary" type="submit" value="Updaten" name="update" id="basketupdate" />
            <input class="btn btn-secondary" type="button" value="Verder winkelen" name="prev" onclick="location.href='<?php echo isset($_SESSION["shop_continue_url"])?$_SESSION["shop_continue_url"]:'/catalogus' ?>';" />
            <input class="btn btn-primary" type="submit" value="Bestellen" name="pay" id="basketorder" style="float:right;" />
            <input class="btn btn-primary" type="submit" value="Offerte" name="tender" style="float:right; margin-right: 10px;" />

          </div>

        </div>
      </form>
    <?php endif; ?>


  </div>
</section>

<script type="text/javascript">
  $(document).ready(function () {
    $(".productsize").keydown(function (event) {
      // Allow: backspace, delete, tab and escape
      if (event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27 ||
        // Allow: Ctrl+A
        (event.keyCode == 65 && event.ctrlKey === true) ||
        // Allow: home, end, left, right
        (event.keyCode >= 35 && event.keyCode <= 39) ||
        // Allow: dot (.) dot (.) (numeric keyboard) comma (,)
        (event.keyCode == 190 || event.keyCode == 110 || event.keyCode == 188)) {
        // let it happen, don't do anything
      }
      else {
        // Ensure that it is a number and stop the keypress
        if ((event.keyCode < 48 || event.keyCode > 57) && (event.keyCode < 96 || event.keyCode > 105 )) {
          event.preventDefault();
        }
      }
    });
    $(".productsize").change(function () {
      if (!isInt($(this).val())) {
        var val = Math.ceil($(this).val());
        if (isNaN(val)) {
          val = '';
        }
        $(this).val(val);
      }
    });
    $(".delproduct").click (function(e) {
      var prodid = $(this).attr("id").substring(6);
      e.preventDefault();
      swal({
        title: 'Verwijderen',
        html: "Wilt u dit product verwijderen uit het winkelmandje?\n" + $("#prodlink_" + prodid).text(),
        type: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Verwijderen',
        cancelButtonText: 'Annuleren'
      }).then(function(result) {
        if (result.value) {
          $.getJSON("<?php echo reconstructQuery() ?>&action=deleteproduct&key=" + prodid,
            function (data) {
              $(".message").remove();
              if (data.result == "ok") {
                $("#basket").before('<div class="alert alert-success alerttop" style="margin-bottom: 10px;"><span class="fa fa-check"></span> ' + data.message + '</div>');
                $("#prodlink_" + prodid).parent().parent().remove();
                $("#subtotal").text("€ " + decimalNL(getFloatEsc($("#subtotal").text().replace('€', '')) - getFloatEsc(data.amount)));
              }
              else {
                $("#basket").before('<div class="messagered" style="margin-bottom: 10px;">Er is iets mis gegaan, probeer het nogmaals.</div>');
              }
            })
        }
      }).catch(swal.noop);
    });

    $(".refreshbasket").click(function (e) {
      e.preventDefault();
      $("#basketupdate").click();
    });


    $(".plusbasket").click(function (event) {
      event.preventDefault();
//      var size = $(this).parent().prev().find(".productsize");
//      if (!size.length)
      var size = $(this).parent().find(".productsize");
      if (size.val() != "") {
        size.val(parseInt(size.val()) + 1);
      }
      else {
        size.val(1);
      }
    });

    $(".minbasket").click(function (event) {
      event.preventDefault();
//      var size = $(this).parent().prev().find(".productsize");
//      if (!size.length)
      var size = $(this).parent().find(".productsize");
      if (size.val() != "") {
        var newval = parseInt(size.val()) - 1;
        if (newval <= 0) {
          size.val(0);
        }
        else {
          size.val(newval);
        }
      }
      else {
        size.val(0);
      }
    });

    <?php if($popup): ?>
      $("#basketorder").click(function(event) {
        event.preventDefault();
        const message = <?php echo json_encode($popup->content->content); ?>;

        swal({
          title: <?php echo json_encode($popup->content->title); ?>,
          html: message,
          type: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Toch bestellen',
          cancelButtonText: 'Annuleren'
        }).then(function(result) {
          if (result.value) {
            // User confirmed, submit the form with the pay action
            const form = $(".basket-form");
            $('<input>').attr({
              type: 'hidden',
              name: 'pay',
              value: 'Bestellen'
            }).appendTo(form);
            form.submit();
          }
        }).catch(swal.noop);
      });
    <?php endif; ?>
  });
</script>
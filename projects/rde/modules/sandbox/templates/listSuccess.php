<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
?>

<Br/>Op deze pagina vindt u een overzicht van de sandbox gebruikers.<br/><br/>

<form method="post" action="<?php echo reconstructQueryAdd() ?>">
  <div class="box">
    <input type="text" name="sb_search" value="<?php echo $_SESSION['sb_search'] ?>" placeholder="Zoeken..."/>
    <select name="sb_type">
      <option value="" <?php writeIfSelectedVal($_SESSION['sb_type'], ""); ?>>Filter op...</option>
      <option value="link" <?php writeIfSelectedVal($_SESSION['sb_type'], "link"); ?>>Te koppelen</option>
      <option value="comp" <?php writeIfSelectedVal($_SESSION['sb_type'], "comp"); ?>>Bedrijven</option>
      <option value="part" <?php writeIfSelectedVal($_SESSION['sb_type'], "part"); ?>>Particulieren</option>
    </select>
    <input type="submit" name="go" value="Zoeken"/>
    <?php echo showHelpButton("Je kunt filteren op:
      - Te koppelen: alle nog te koppelen gebruikers
      - Bedrijven: alleen zoeken door bedrijven
      - Particulieren: alleen zoeken door particulieren") ?>
  </div>

  <?php $pager->writePreviousNext() ?>

  <?php if (count($sandboxusers) == 0): ?>
    <br/>
    Er zijn geen gebruikers in de zandbak gevonden. (misschien zitten ze op de schommel? ;-))
  <?php else: ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Bedrijfsnaam</td>
        <td>Naam</td>
        <td>E-mailadres</td>
        <td>Plaats</td>
        <td>Aangemaakt op</td>
        <td>Particulier</td>
        <td>Geblokkeerd</td>
        <td style="width:100px;">Actie</td>
        <td>Koppelen</td>
      </tr>
      <?php
        /** @var SandboxUsers $sbu */
        foreach ($sandboxusers as $sbu): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $sbu->companyName ?></td>
            <td><?php echo $sbu->getNaam() ?></td>
            <td><?php echo $sbu->email ?></td>
            <td><?php echo $sbu->domestic ?></td>
            <td><?php echo $sbu->getCreated() ?></td>
            <td style="text-align: center">
              <?php if ($sbu->isPrivate()): ?>
                <?php echo IconHelper::getCheckbox() ?>
              <?php endif; ?>
            </td>
            <td style="text-align: center">
              <?php if ($sbu->blocked == "true"): ?>
                <span title="Geblokkeerd"><?php echo IconHelper::getLock() ?></span>
              <?php endif; ?>
              <?php if ($sbu->blockedbyadmin == "true"): ?>
                <span class="icon-color-red" title="Geblokkeerd door bart"><?php echo IconHelper::getLock() ?></span>
              <?php endif; ?>
            </td>
            <td>
              <?php if ($sbu->companyId != ""): ?>
                <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=companyedit&id=' . $sbu->userId) ?>
              <?php else: ?>
                <?php if ($sbu->personId == ""): ?>
                  <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=useredit&id=' . $sbu->userId) ?>
                <?php endif; ?>
              <?php endif; ?>
              <?php echo BtnHelper::getLogin($sbu->getLoginLink(), "Inloggen op raamdorpel.nl als deze klant", "_blank") ?>
            </td>
            <td>
              <?php if ($sbu->private == "false" && $sbu->companyId == ""): ?>
                <a href="<?php echo reconstructQueryAdd(['pageId']) ?>action=companysearch&id=<?php echo $sbu->userId ?>" class="gsd-btn gsd-btn-primary">
                  Koppelen
                </a>
              <?php endif; ?>
            </td>
          </tr>
        <?php endforeach; ?>
    </table>
    <br/>
    <?php $pager->writePreviousNext() ?>

  <?php endif; ?>

</form>

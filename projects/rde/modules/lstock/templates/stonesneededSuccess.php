<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
  <form method="post">
    <div class="box">
      <input type="text" name="st_search" value="<?php echo $_SESSION['st_search'] ?>" placeholder="Zoeken..."/>
      <select name="st_brand">
        <option value="">Selecteer leverancier...</option>
        <?php foreach($stonebrands as $sb): ?>
          <option value="<?php echo $sb->brandId ?>" <?php if($_SESSION['st_brand']==$sb->brandId) echo 'selected'; ?>><?php echo $sb->name ?></option>
        <?php endforeach; ?>
      </select>
      <input type="submit" name="go" value="Zoeken" />

    </div>
  </form>

	<?php	if(count($stones)==0): ?>
		<br/>Er zijn geen stenen gevonden
  <?php else: ?>
		<table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Productnaam</td>
        <td style="text-align: right;width: 50px;">M</td>
        <td style="text-align: right;width: 50px;">EL</td>
        <td style="text-align: right;width: 50px;">ER</td>
        <td style="text-align: right;width: 50px;">G</td>
        <td style="text-align: right;width: 50px;">Totaal</td>
        <td style="text-align: right;width: 50px;">Detail</td>
      </tr>
		<?php
      $total["totalMiddlesStones"] = 0;
      $total["totalLeftEndStones"] = 0;
      $total["totalRightEndStones"] = 0;
      $total["totalEndStonesGrooves"] = 0;
      foreach($stones as $item):
        $total["totalMiddlesStones"] += $item->totals["totalMiddlesStones"];
        $total["totalLeftEndStones"] += $item->totals["totalLeftEndStones"];
        $total["totalRightEndStones"] += $item->totals["totalRightEndStones"];
        $total["totalEndStonesGrooves"] += $item->totals["totalEndStonesGrooves"];
        ?>
			<tr class="dataTableRow trhover">
        <td><?php echo $item->name ?></td>
        <td style="text-align: right;"><?php echo $item->totals["totalMiddlesStones"] ?></td>
        <td style="text-align: right;"><?php echo $item->totals["totalLeftEndStones"] ?></td>
        <td style="text-align: right;"><?php echo $item->totals["totalRightEndStones"]  ?></td>
        <td style="text-align: right;"><?php if($item->totals["totalEndStonesGrooves"]!==0) echo $item->totals["totalEndStonesGrooves"] ?></td>
        <td style="text-align: right;"><?php echo $item->totals["sumorder"] ?></td>
        <td style="text-align: right;"><a href="#" class="fa fa-info-circle showdetail" data-id="<?php echo $item->stoneId; ?>" style="font-size: 14px;"></a> </td>
      </tr>
      <tr class="dataTableRow trhover topborder hidden order-<?php echo $item->stoneId; ?>">
        <td style="text-align: right;">Voorraad:</td>
        <td style="text-align: right;"><?php echo $item->stock ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->stock ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->stock ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->stock ?></td>
        <td></td><td></td>
      </tr>
      <tr class="dataTableRow trhover hidden order-<?php echo $item->stoneId; ?>">
        <td style="text-align: right;">Voorraad marge:</td>
        <td style="text-align: right;"><?php echo $item->minAmountStones ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->minAmountStones ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->minAmountStones ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->minAmountStones ?></td>
        <td></td><td></td>
      </tr>
      <tr class="dataTableRow trhover hidden order-<?php echo $item->stoneId; ?>">
        <td style="text-align: right;">Op te bestellen lijst:</td>
        <td style="text-align: right;"><?php echo $item->onOrderlist ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->onOrderlist ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->onOrderlist ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->onOrderlist ?></td>
        <td></td><td></td>
      </tr>
      <tr class="dataTableRow trhover hidden order-<?php echo $item->stoneId; ?> bottomborder">
        <td style="text-align: right;">Besteld, niet geleverd:</td>
        <td style="text-align: right;"><?php echo $item->alreadyOrdered ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->alreadyOrdered ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->alreadyOrdered ?></td>
        <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->alreadyOrdered ?></td>
        <td></td>
        <td></td>
      </tr>
    <?php endforeach; ?>
      <tr class="dataTableRow trhover topborder">
        <td>Totaal alle stenen:</td>
        <td style="text-align: right;"><?php echo $total["totalMiddlesStones"]  ?></td>
        <td style="text-align: right;"><?php echo $total["totalLeftEndStones"]  ?></td>
        <td style="text-align: right;"><?php echo $total["totalRightEndStones"]  ?></td>
        <td style="text-align: right;"><?php echo $total["totalEndStonesGrooves"]  ?></td>
        <td style="text-align: right;"><?php echo $total["totalMiddlesStones"]+$total["totalLeftEndStones"]+$total["totalRightEndStones"]+$total["totalEndStonesGrooves"]  ?></td>
        <td></td>
      </tr>
		</table><br/>
	<?php endif; ?>



<script type="text/javascript">
  $(document).ready(function() {
    $(".showdetail").click(function() {
      var id = $(this).attr("data-id");
      if($(".order-"+id).hasClass("hidden")) {
        $(".order-"+id).removeClass("hidden");
      }
      else {
        $(".order-"+id).addClass("hidden");
      }
    });
  });
</script>


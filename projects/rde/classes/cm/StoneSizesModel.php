<?php

  AppModel::loadBaseClass('BaseStoneSizes');

  class StoneSizesModel extends BaseStoneSizes {

    public function isOpmaatgemaakt() {
      return stripos($this->short, "Op maat gemaakt") !== false;
    }

    public static function getSizes($brandId = ""): array {
      if ($brandId != "") {
        return StoneSizes::find_all_by(["brandId" => $brandId], "ORDER BY brandId, name");
      }
      return StoneSizes::find_all("ORDER BY brandId, name");
    }

    public static function getDisplayStonesizes() {
      return StoneSizes::find_all_by(["display" => 'true'], "ORDER BY stone_sizes.common ASC,stone_sizes.name ASC ");
    }

    /**
     * Get sizes with stones
     * @param int[]|bool $brandIds
     * @param string|bool $type
     * @return StoneSizes[]
     */
    public static function getDisplayStonesizesWithstone($brandIds = false, $type = false) {
      $query = "SELECT * FROM " . StoneSizes::getTablename() . " ";
      $query .= "JOIN " . Stones::getTablename() . " ON stones.sizeId = stone_sizes.sizeId ";
      $query .= "WHERE stone_sizes.display='true' AND stones.display='true' ";
      if ($type !== false) {
        $query .= "AND stones.type='" . $type . "' ";
      }
      if ($brandIds !== false) {
        $query .= "AND stone_sizes.brandId IN (" . implode(",", $brandIds) . ") ";
      }
      $query .= "ORDER BY stone_sizes.common ASC, stone_sizes.name ASC ";

      $result = DBConn::db_link()->query($query);
      $sois = [];
      while ($row = $result->fetch_row()) {
        $soi = new StoneSizes();
        $soi->hydrate($row);
        $soi->from_db = true;

        $stone = new Stones();
        $stone->hydrate($row, count(StoneSizes::columns));
        $stone->from_db = true;
        $soi->stone = $stone;
        $sois[] = $soi;
      }

      return $sois;
    }

    /**
     * Get sizes with stones by stoneCategoryId
     * @param int $stoneCategoryId : stone.category_id
     * @param int[]|bool $brandIds : extra brands filter
     * @return StoneSizes[]
     */
    public static function getStonesizesByCategoryId($stoneCategoryId, $brandIds = false): array {
      $query = "SELECT * FROM " . StoneSizes::getTablename() . " ";
      $query .= "JOIN " . Stones::getTablename() . " ON stones.sizeId = stone_sizes.sizeId ";
      $query .= "WHERE stone_sizes.display='true' AND stones.display='true' ";
      $query .= "AND stones.category_id=" . escapeForDB($stoneCategoryId) . " ";
      if ($brandIds !== false) {
        $query .= "AND stone_sizes.brandId IN (" . implode(",", $brandIds) . ") ";
      }
      $query .= "ORDER BY stone_sizes.common ASC, stone_sizes.name ASC ";

      $result = DBConn::db_link()->query($query);
      $sois = [];
      while ($row = $result->fetch_row()) {
        $soi = new StoneSizes();
        $soi->hydrate($row);
        $soi->from_db = true;

        $stone = new Stones();
        $stone->hydrate($row, count(StoneSizes::columns));
        $stone->from_db = true;
        $soi->stone = $stone;
        $sois[] = $soi;
      }

      return $sois;
    }

    public function getSizeString() {
      return $this->length . ' x ' . $this->width . ' x ' . $this->height;
    }

  }
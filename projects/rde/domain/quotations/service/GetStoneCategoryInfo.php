<?php

  namespace domain\quotations\service;

  use StoneCategory;
  use Stones;

  class GetStoneCategoryInfo {

    private $category;
    private $categoryPath; //array met StoneCategories ordered, from top to bottom sorted

    /**
     * GetExtraProducts constructor.
     * @param $stoneQuatationId
     */
    public function __construct($stoneQuatationId) {
      if ($stoneQuatationId != "" && $stoneQuatationId != 0) {
        $this->category = StoneCategory::find_by_id($stoneQuatationId);
        $this->categoryPath = array_reverse(StoneCategory::getParents($this->category));
      }
    }

    public function getType() {
      if ($this->isRaamdorpel()) {
        return Stones::TYPE_RAAMDORPEL;
      }
      if ($this->isSpekband()) {
        return Stones::TYPE_SPEKBAND;
      }
      if ($this->isMuurafdekker()) {
        return Stones::TYPE_MUURAFDEKKER;
      }
      if ($this->isVensterbank()) {
        return Stones::TYPE_VENSTERBANK;
      }
      if ($this->isBalkje()) {
        return Stones::TYPE_BALKJES;
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isRaamdorpel() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "raamdorpel") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isMuurafdekker() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "muurafdekker") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isSpekband() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "spekband") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isVensterbank() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "vensterbank") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isBalkje() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "balkje") !== false) {
          return true;
        }
      }
      return false;
    }

    public function getMaterial() {
      if ($this->isKeramiek()) {
        return Stones::MATERIAL_KERAMIEK;
      }
      if ($this->isBeton()) {
        return Stones::MATERIAL_BETON;
      }
      if ($this->isNatuursteen()) {
        return Stones::MATERIAL_NATUURSTEEN;
      }
      if ($this->isIsosill()) {
        return Stones::MATERIAL_ISOSILL;
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isKeramiek() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "keramiek") !== false || stripos($cat->name, "keramisch") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isBeton() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "beton") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isNatuursteen() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "natuursteen") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isIsosill() {
      if (!$this->category) return false;
      foreach ($this->categoryPath as $cat) {
        if (stripos($cat->name, "isosill") !== false) {
          return true;
        }
      }
      return false;
    }

    /**
     * @return string|false
     */
    public function isNatuursteenMuurafdekkerPlat() {
      if (!$this->category) return false;
      if (!($this->isNatuursteen() && $this->isMuurafdekker())) return false;

      foreach ($this->categoryPath as $cat) {
        if ($cat->id == 17) { //Natuursteen muurafdekker plat
          return true;
        }
      }
      return false;
    }


    /**
     * @return StoneCategory
     */
    public function getCategory(): StoneCategory {
      return $this->category;
    }

    /**
     * @param StoneCategory $category
     */
    public function setCategory(StoneCategory $category): void {
      $this->category = $category;
    }

    /**
     * @return StoneCategory[]|null
     */
    public function getCategoryPath(): ?array {
      return $this->categoryPath;
    }

    /**
     * @param StoneCategory[]|null $categoryPath
     */
    public function setCategoryPath(?array $categoryPath): void {
      $this->categoryPath = $categoryPath;
    }


  }
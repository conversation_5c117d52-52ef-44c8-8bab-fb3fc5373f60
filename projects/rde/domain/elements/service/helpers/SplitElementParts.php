<?php

  namespace domain\elements\service\helpers;

  use DBConn;
  use Mitres;
  use QuotationsExtra;

  class SplitElementParts {

    public $divisionMeasure, $stoneSizeWidth, $maxMeasureElement, $mitreLengthSizeLeft, $mitreLengthSizeRight, $mitreStoneCountLeft, $mitreStoneCountRight, $mitreAngleRight, $mitreAngleLeft, $stoneId, $stoneAmount, $heartClickSize, $heartLengthLeft, $heartLengthRight, $inputLength, $dbElementLength, $shorter, $rightMitreId, $leftMitreId = null;
    private const VOEG = 3.5;

    /**
     * Retrieves a Mitres object based on mitreId or a combination of angle and stoneId.
     * Returns null if no valid parameters are provided.
     */
    public function setMitreElementPartsReturnObject($mitreId, $angle, $stoneId): ?Mitres {
      if (!($mitreId > 0 || ($angle >= 30 && $angle <= 150 && $stoneId > 0))) {
        return null;
      }

      $query = "SELECT M.* FROM " . Mitres::getTablename() . " M";
      if ($angle > 0 && $stoneId > 0) {
        $query .= " INNER JOIN `rde_b1mo`.`stone_sizes` TS ON M.`stoneLength` = TS.`length`";
        $query .= " INNER JOIN `rde_b1mo`.`stones` S ON TS.`sizeId` = S.`sizeId`";
        $query .= " WHERE S.`stoneId` = '$stoneId' AND M.`angle` = '$angle'";
        $query .= " LIMIT 0,1";
      } elseif ($mitreId > 0) {
        $query .= " WHERE M.`mitreId` = '$mitreId'";
        $query .= " LIMIT 0,1";
      }

      $result = DBConn::db_link()->query($query);
      $row = $result->fetch_array();
      return (new Mitres())->hydrateNext($row);
    }

    public function isSmallerThanMaxMeasureElement($elementCheck, $maxMeasureElement): bool {
      return $elementCheck < $maxMeasureElement;
    }

    public function getMitreAmountAndSide($mitreLengthSizeLeft, $mitreLengthSizeRight): array {
      $mitre = ['amount' => 0, 'side' => 'none'];

      if ($mitreLengthSizeLeft && $mitreLengthSizeRight) {
        $mitre['amount'] = 2;
        $mitre['side'] = 'both';
      } elseif ($mitreLengthSizeLeft && !$mitreLengthSizeRight) {
        $mitre['amount'] = 1;
        $mitre['side'] = 'left';
      } elseif (!$mitreLengthSizeLeft && $mitreLengthSizeRight) {
        $mitre['amount'] = 1;
        $mitre['side'] = 'right';
      }

      return $mitre;
    }

    public function elementCheckMitreOne($element, $mitreLengthSize, $voeg, $shorter): float|int {
      return ($element + $mitreLengthSize) + $voeg + ($shorter / 2);
    }

    public function elementCheckMitreTwo($element, $mitreLengthSize, $voeg): int {
      return ($element + $mitreLengthSize) + 2 + $voeg;
    }

    /**
     * Calculates the amount of stones for part A based on mitre amount and total stone count.
     */
    public function getStoneAmountAByMitreAmount($mitre, $partNr, $totalStoneAmountMinMitre): float|int {
      return match ($mitre['amount']) {
        1 => floor($totalStoneAmountMinMitre / $partNr),
        default => ceil($totalStoneAmountMinMitre / $partNr)
      };
    }

    /**
     * Calculates the input length and spacing based on heart click size and mitre/heart lengths.
     * Returns an array with adjusted input length and total spacing.
     */
    private function calculateInputLengthAndSpacing($heartClickSize, $mitreLeftLongLength, $mitreRightLongLength, $heartLengthLeft, $heartLengthRight, $voeg): array {
      $hasBothMitres = ($mitreLeftLongLength !== 0 && $mitreRightLongLength !== 0) ||
        ($heartLengthLeft !== 0 && $heartLengthRight !== 0);
      $hasOneMitre = ($mitreLeftLongLength !== 0 || $mitreRightLongLength !== 0) ||
        ($heartLengthLeft !== 0 || $heartLengthRight !== 0);

      if ($heartClickSize === '1') {
        // Top aan de punt gemeten
        if (!$hasOneMitre) {
          return [$this->inputLength, $mitreLeftLongLength + $mitreRightLongLength];
        } elseif (!$hasBothMitres) {
          return [$this->inputLength - 2, $mitreLeftLongLength + $mitreRightLongLength + $voeg];
        } else {
          return [$this->inputLength - 4, $mitreLeftLongLength + $mitreRightLongLength + $voeg + $voeg];
        }
      } else {
        // Heartclick gemeten
        if (!$hasOneMitre) {
          return [$this->inputLength, $heartLengthLeft + $heartLengthRight];
        } elseif (!$hasBothMitres) {
          return [$this->inputLength - 2, $heartLengthLeft + $heartLengthRight + $voeg];
        } else {
          return [$this->inputLength - 4, $heartLengthLeft + $heartLengthRight + $voeg + $voeg];
        }
      }
    }

    /**
     * Calculates the element lengths for each part, depending on the number of parts and fit stone configuration.
     * Returns an array with calculated element lengths.
     */
    public function fitStoneElementCalculator($fitStoneAmount, $stoneAmountArray, $fitStoneSplit, $stoneWidthX10, $voeg, $fitStoneLength, $dbElementLength, $totalStoneAmountMinMitre, $heartLengthLeft, $heartLengthRight, $heartClickSize, $mitreLeftLongLength, $mitreRightLongLength): array {
      $elements = [];
      $partCount = count($stoneAmountArray);

      if ($partCount === 2) {
        if ($fitStoneAmount > 0) {
          [$inputLengthAndSpacing, $lengthAndVoeg] = $this->calculateInputLengthAndSpacing($heartClickSize, $mitreLeftLongLength, $mitreRightLongLength, $heartLengthLeft, $heartLengthRight, $voeg);

          // Account for shorter value before division
          $bothElementsConnected = ($inputLengthAndSpacing - $lengthAndVoeg - $this->shorter) - $voeg;
          $splitElement = ($bothElementsConnected / 2) + ($this->shorter / 2);

          if ($stoneAmountArray['stoneAmountA'] == $stoneAmountArray['stoneAmountB']) {
            $elements['aElement'] = $splitElement;
            $elements['bElement'] = $splitElement;
          } else {
            // Corrected calculation: account for joints properly
            $jointsA = ($stoneAmountArray['stoneAmountA'] - $fitStoneSplit - 1) * $voeg;
            $jointsB = ($stoneAmountArray['stoneAmountB'] - $fitStoneSplit - 1) * $voeg;
            $elements['aElement'] = (($stoneAmountArray['stoneAmountA'] - $fitStoneSplit) * $stoneWidthX10) + $jointsA + $fitStoneLength;
            $elements['bElement'] = (($stoneAmountArray['stoneAmountB'] - $fitStoneSplit) * $stoneWidthX10) + $jointsB + $fitStoneLength;
          }
        } else {
          $this->calculateElementsWithoutFitStone($elements, $stoneAmountArray, $dbElementLength, $voeg, $totalStoneAmountMinMitre);
        }
      } elseif ($partCount === 3) {
        $this->calculateThreePartElements($elements, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit, $voeg, $fitStoneLength, $dbElementLength, $totalStoneAmountMinMitre);
      } elseif ($partCount >= 4) {
        $this->calculateFourOrMorePartElements($elements, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit, $voeg, $fitStoneLength, $dbElementLength, $totalStoneAmountMinMitre);
      }

      return $elements;
    }

    /**
     * Helper for calculating element lengths when no fit stone is present.
     * Modifies the $elements array in place.
     */
    private function calculateElementsWithoutFitStone(&$elements, $stoneAmountArray, $dbElementLength, $voeg, $totalStoneAmountMinMitre): void {
      // Calculate the effective stone width including joint
      // The total element length includes joints between stones, so we need to account for this
      // For n stones, there are (n-1) joints between them
      $totalJoints = ($totalStoneAmountMinMitre - 1) * $voeg;
      $effectiveStoneWidth = ($dbElementLength - $totalJoints) / $totalStoneAmountMinMitre;

      foreach ($stoneAmountArray as $key => $amount) {
        $elementKey = str_replace('stoneAmount', '', $key) . 'Element';
        // Calculate element length: (stone_width * amount) + (joints between stones)
        $jointsInElement = ($amount - 1) * $voeg;
        $elements[$elementKey] = ($amount * $effectiveStoneWidth) + $jointsInElement;
      }
    }

    private function calculateThreePartElements(&$elements, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit, $voeg, $fitStoneLength, $dbElementLength, $totalStoneAmountMinMitre): void {
      if ($fitStoneAmount > 0) {
        // Corrected calculation: account for joints properly
        $jointsA = ($stoneAmountArray['stoneAmountA'] - $fitStoneSplit - 1) * $voeg;
        $jointsB = ($stoneAmountArray['stoneAmountB'] - 1) * $voeg;
        $jointsC = ($stoneAmountArray['stoneAmountC'] - $fitStoneSplit - 1) * $voeg;

        $elements['aElement'] = (($stoneAmountArray['stoneAmountA'] - $fitStoneSplit) * ($this->divisionMeasure - $voeg)) + $jointsA + $fitStoneLength;
        $elements['bElement'] = ($stoneAmountArray['stoneAmountB'] * ($this->divisionMeasure - $voeg)) + $jointsB;
        $elements['cElement'] = (($stoneAmountArray['stoneAmountC'] - $fitStoneSplit) * ($this->divisionMeasure - $voeg)) + $jointsC + $fitStoneLength;
      } else {
        $this->calculateElementsWithoutFitStone($elements, $stoneAmountArray, $dbElementLength, $voeg, $totalStoneAmountMinMitre);
      }
    }

    private function calculateFourOrMorePartElements(&$elements, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit, $voeg, $fitStoneLength, $dbElementLength, $totalStoneAmountMinMitre): void {
      if ($fitStoneAmount > 0) {
        // Corrected calculation: account for joints properly
        $jointsA = ($stoneAmountArray['stoneAmountA'] - $fitStoneSplit - 1) * $voeg;
        $jointsB = ($stoneAmountArray['stoneAmountB'] - 1) * $voeg;
        $jointsC = ($stoneAmountArray['stoneAmountC'] - 1) * $voeg;
        $jointsD = ($stoneAmountArray['stoneAmountD'] - $fitStoneSplit - 1) * $voeg;

        $elements['aElement'] = (($stoneAmountArray['stoneAmountA'] - $fitStoneSplit) * ($this->divisionMeasure - $voeg)) + $jointsA + $fitStoneLength;
        $elements['bElement'] = ($stoneAmountArray['stoneAmountB'] * ($this->divisionMeasure - $voeg)) + $jointsB;
        $elements['cElement'] = ($stoneAmountArray['stoneAmountC'] * ($this->divisionMeasure - $voeg)) + $jointsC;
        $elements['dElement'] = (($stoneAmountArray['stoneAmountD'] - $fitStoneSplit) * ($this->divisionMeasure - $voeg)) + $jointsD + $fitStoneLength;
      } else {
        $this->calculateElementsWithoutFitStone($elements, $stoneAmountArray, $dbElementLength, $voeg, $totalStoneAmountMinMitre);
      }
    }

    private function initializeElementParts(): array {
      return [
        'allComparesAreTrue' => 'false',
        'aAmount1' => '', 'aLength1' => '', 'aLength2' => '',
        'bAmount1' => '', 'bLength1' => '', 'bLength2' => '',
        'cAmount1' => '', 'cLength1' => '', 'cLength2' => '',
        'dAmount1' => '', 'dLength1' => '', 'dLength2' => ''
      ];
    }

    /**
     * Retrieves mitre data (heart, short, long lengths and stone count) for a given mitreId and angle.
     * Returns default values if no mitre is found.
     */
    private function getMitreData($mitreId, $angle): array {
      $mitre = $this->setMitreElementPartsReturnObject($mitreId, $angle, $this->stoneId);

      if ($mitre) {
        return [
          'heartLength' => $mitre->heartLength,
          'shortLength' => $mitre->shortLength,
          'longLength' => $mitre->longLength,
          'stoneCount' => $mitre->stoneCount
        ];
      }

      return [
        'heartLength' => 0,
        'shortLength' => 0,
        'longLength' => 0,
        'stoneCount' => 0
      ];
    }

    /**
     * Orchestrates the calculation and validation of element parts and their checks.
     * Returns an array with element parts and their check values.
     */
    private function processElementParts($partNr, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit, $stoneWidthX10, $fitStoneLength, $totalStoneAmountMinMitre, $mitreLeftLongLength, $mitreRightLongLength, $mitre): array {
      $clsCheckMitreElementParts = new CheckMitreElementParts();
      $clsAddValuesToAElementCheck = new AddValuesToElementCheck();
      $clsAddValuesToAElementParts = new AddValuesToElementParts();

      $elementsFitStoneCalc = $this->fitStoneElementCalculator(
        $fitStoneAmount, $stoneAmountArray, $fitStoneSplit, $stoneWidthX10, self::VOEG, $fitStoneLength, $this->dbElementLength,
        $totalStoneAmountMinMitre, $this->heartLengthLeft, $this->heartLengthRight, $this->heartClickSize, $mitreLeftLongLength, $mitreRightLongLength
      );

      $aElements = array_values($elementsFitStoneCalc);

      $mitreElementParts = $clsCheckMitreElementParts->checkMitreElementParts(
        $aElements, $mitre, self::VOEG, $this->mitreLengthSizeLeft,
        $this->mitreLengthSizeRight, $this->shorter
      );

      $arrayElementCheck = $clsAddValuesToAElementCheck->addValuesToAElementCheck(
        $partNr, $mitreElementParts, $stoneAmountArray['stoneAmountB'] ?? 0
      );

      $aElementParts = $clsAddValuesToAElementParts->addValuesToAElementParts(
        $partNr, $mitreElementParts, $stoneAmountArray['stoneAmountB'] ?? 0, $this->initializeElementParts()
      );

      return [$aElementParts, $arrayElementCheck];
    }

    /**
     * Validates that all element check values are smaller than the maximum allowed measure.
     * Returns true if all checks pass, false otherwise.
     */
    private function validateElementChecks($arrayElementCheck, $partNr): bool {
      $checks = ['aElementCheck', 'bElementCheck'];
      if ($partNr >= 3) $checks[] = 'cElementCheck';
      if ($partNr >= 4) $checks[] = 'dElementCheck';

      foreach ($checks as $check) {
        if (!$this->isSmallerThanMaxMeasureElement($arrayElementCheck[$check], $this->maxMeasureElement)) {
          return false;
        }
      }
      return true;
    }

    /**
     * Calculates and returns the element parts for cases with four or more parts.
     * Handles both whole and non-whole stone amount B cases.
     */
    public function fourOrMoreParts($totalStoneAmountMinMitre, $stoneAmountA, $partNr, $fitStoneAmount, $fitStoneSplit, $stoneWidthX10, $fitStoneLength, $mitre, $mitreLeftLongLength, $mitreRightLongLength) {
      $splitNumberBStone = $partNr - 2;
      $stoneAmountB = ($totalStoneAmountMinMitre - ($stoneAmountA * 2)) / $splitNumberBStone;

      if (floor($stoneAmountB) == $stoneAmountB) {
        $stoneAmountArray = [
          'stoneAmountA' => $stoneAmountA,
          'stoneAmountB' => $stoneAmountB,
          'stoneAmountC' => $stoneAmountA
        ];
      } else {
        $stoneAmountArray = [
          'stoneAmountA' => $stoneAmountA,
          'stoneAmountB' => round($stoneAmountB),
          'stoneAmountC' => $totalStoneAmountMinMitre - ((round($stoneAmountB) * ($splitNumberBStone - 1)) + (round($stoneAmountA) * 2)),
          'stoneAmountD' => round($stoneAmountA)
        ];
      }

      [$elementParts, $arrayElementCheck] = $this->processElementParts(
        $partNr, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit,
        $stoneWidthX10, $fitStoneLength, $totalStoneAmountMinMitre,
        $mitreLeftLongLength, $mitreRightLongLength, $mitre
      );

      if ($this->validateElementChecks($arrayElementCheck, $partNr)) {
        $elementParts['allComparesAreTrue'] = 'true';
      }

      return $elementParts;
    }

    /**
     * Main entry point for splitting element parts based on the number of parts and quotation data.
     * Returns the calculated element parts array.
     */
    public function splitElementParts($partNr, $quotationExtra, $element, $stoneSize) {
      if (!$quotationExtra) {
        return false;
      }

      $aElementParts = $this->initializeElementParts();
      $stoneWidthX10 = $quotationExtra->stoneSizeWidth * 10;

      $leftMitreData = $this->getMitreData($this->leftMitreId, $this->mitreAngleLeft);
      $rightMitreData = $this->getMitreData($this->rightMitreId, $this->mitreAngleRight);

      // Bepaal fit stone data
      $element->calculateFitStone($stoneSize->width);
      $fitStoneAmount = $element->fitStoneAmount;
      $fitStoneSplit = $element->fitStoneAmount > 0 ? $element->fitStoneAmount / 2 : 0;
      $fitStoneLength = $element->fitStoneLength ?? $stoneWidthX10;

      $mitre = $this->getMitreAmountAndSide($this->mitreLengthSizeLeft, $this->mitreLengthSizeRight);
      $totalStoneAmountMinMitre = $this->stoneAmount - $this->mitreStoneCountLeft - $this->mitreStoneCountRight;
      $stoneAmountA = $this->getStoneAmountAByMitreAmount($mitre, $partNr, $totalStoneAmountMinMitre);

      // Process verschillende part numbers
      if ($partNr >= 4) {
        $aElementParts = $this->fourOrMoreParts(
          $totalStoneAmountMinMitre, $stoneAmountA, $partNr, $fitStoneAmount,
          $fitStoneSplit, $stoneWidthX10, $fitStoneLength, $mitre, $leftMitreData['longLength'], $rightMitreData['longLength']
        );
      } elseif ($partNr === 3) {
        $stoneAmountArray = [
          'stoneAmountA' => $stoneAmountA,
          'stoneAmountB' => $totalStoneAmountMinMitre - ($stoneAmountA * 2),
          'stoneAmountC' => $stoneAmountA
        ];

        [$aElementParts, $arrayElementCheck] = $this->processElementParts(
          $partNr, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit,
          $stoneWidthX10, $fitStoneLength, $totalStoneAmountMinMitre,
          $leftMitreData['longLength'], $rightMitreData['longLength'], $mitre
        );

        if ($this->validateElementChecks($arrayElementCheck, $partNr)) {
          $aElementParts['allComparesAreTrue'] = 'true';
        }
      } elseif ($partNr === 2) {
        $stoneAmountArray = [
          'stoneAmountA' => $stoneAmountA,
          'stoneAmountB' => $totalStoneAmountMinMitre - $stoneAmountA
        ];

        [$aElementParts, $arrayElementCheck] = $this->processElementParts(
          $partNr, $stoneAmountArray, $fitStoneAmount, $fitStoneSplit,
          $stoneWidthX10, $fitStoneLength, $totalStoneAmountMinMitre,
          $leftMitreData['longLength'], $rightMitreData['longLength'], $mitre
        );

        if ($this->validateElementChecks($arrayElementCheck, $partNr)) {
          $aElementParts['allComparesAreTrue'] = 'true';
        }
      }

      return $aElementParts;
    }
  }